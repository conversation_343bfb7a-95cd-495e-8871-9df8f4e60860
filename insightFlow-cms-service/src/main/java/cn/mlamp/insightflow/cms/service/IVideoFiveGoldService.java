package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.CmsVideoFiveGold;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldDetailRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldRetryScriptRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailPageVO;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailVO;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
public interface IVideoFiveGoldService extends IService<CmsVideoFiveGold> {


     Page<VideoFiveGoldVO> getList(VideoFiveGoldRequest videoFiveGoldRequest);

     /**
      * 获取黄金5秒详情列表
      *
      * @param videoFiveGoldDetailRequest 请求参数
      * @return 包含分页数据和额外信息的VO
      */
     VideoFiveGoldDetailPageVO getListDetail(VideoFiveGoldDetailRequest videoFiveGoldDetailRequest);

}
