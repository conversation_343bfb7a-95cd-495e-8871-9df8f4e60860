package cn.mlamp.insightflow.cms.util;

import cn.hutool.core.lang.UUID;

public class FilePathBuilder {

    private static String profile;

    private final static String VIDEO_DECODE_OPEN="/video/decode/";

    private final static String VIDEO_DECODE_OPEN_PROCESSED="/video/decode/processed/";


    private final static String VIDEO_DECODE_CLOSE="/video/file/";


    public static void setProfile(String profile){
        if("test2".equals(profile)){
            profile = "test";
        }

        if("prod2".equals(profile)){
            profile = "prod";
        }
        FilePathBuilder.profile = profile;
    }


    /**
     * 获取开放路径
     *  getVideoDecodeOssPath("fsdfsdfsdf", "rrewrw/34.jpg")
     *  test/video/decode/fsdfsdfsdf/rrewrw/34.jpg
     * @param esId
     * @param ossId
     * @return
     */
    public static String getOpenVideoDecodeOssPath(String esId, String ossId) {
        return new StringBuilder(profile).append(VIDEO_DECODE_OPEN).append(esId).append("/").append(ossId).toString();
    }


    public static String getUserOpenVideoDecodeOssPath(String esId, String ossId) {
        return new StringBuilder(profile).append(VIDEO_DECODE_OPEN_PROCESSED).append(esId).append("/").append(ossId).toString();
    }

    /**
     * 获取非开放路径
     * @param ossId
     * @return
     */
    public static String getCloseVideoDecodeOssPath(String esId, String ossId) {
        return new StringBuilder(profile).append(VIDEO_DECODE_CLOSE).append(ossId).toString();
    }


    public static String getVideoDecodeOssPath(String esId, String ossId,Boolean isOpen) {
        if(isOpen){
            return getOpenVideoDecodeOssPath(esId,ossId);
        }
        return getCloseVideoDecodeOssPath(esId,ossId);
    }


        /**
         * 生成文件在对象存储服务（OSS）中的路径
         * 该方法根据前缀、后缀生成一个唯一的文件名，用于在OSS中存储文件
         * 使用UUID生成一个特殊的值，确保文件名的唯一性，避免命名冲突
         *
         * @param prefix 文件名的前缀部分，用于标识文件的类别或用途
         * @param suffix 文件名的后缀部分，通常是指文件的扩展名，表示文件的类型
         * @return 返回拼接后的完整文件路径字符串
         */
    public static String generateImageOssPath(String prefix, String suffix) {

        // 使用StringBuilder拼接文件名各部分
        return new StringBuilder(profile)
                .append("/")
                .append(prefix)
                .append("_") // 图片服务器格式要求
                .append(UUID.fastUUID()) // 添加特殊值
                .append(suffix)
                .toString();
    }

    public static String generateVideoOssPath(String prefix, String suffix) {

        // 使用StringBuilder拼接文件名各部分
        return new StringBuilder()
                .append(prefix)
                .append("_")  // 图片服务器格式要求
                .append(UUID.fastUUID()) // 添加特殊值
                .append(suffix)
                .toString();
    }

}
