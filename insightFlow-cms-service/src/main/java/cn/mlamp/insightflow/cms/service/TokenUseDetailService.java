package cn.mlamp.insightflow.cms.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import cn.mlamp.insightflow.cms.entity.TokenUseDetail;
import cn.mlamp.insightflow.cms.model.query.ConsumptionQueryDTO;
import cn.mlamp.insightflow.cms.model.vo.ConsumptionVO;
import cn.mlamp.insightflow.cms.model.vo.MobileInfoVO;

public interface TokenUseDetailService extends IService<TokenUseDetail> {
    void countTokenUse(String observationId, Integer taskId, Integer taskType, String taskName, Integer tenantId,
                       String userId, String userName);

    List<TokenUseDetail> getTokenDetailByTypeAndTaskIdIn(Integer taskType, List<Integer> taskIdList);

    void saveTokenDetail(int tokens, String observationId, Integer taskId, Integer taskType,
                                   String taskName, Integer tenantId, Integer userId);

    /**
     * 获取累积消费列表
     * 
     * @param tenantId 租户ID
     * @param keyword  任务名称关键词
     * @return 消费明细列表
     */
    Page<ConsumptionVO> listConsumptions(Integer tenantId, ConsumptionQueryDTO queryDTO);

    Page<ConsumptionVO> listMobileConsumptions(Integer tenantId, ConsumptionQueryDTO queryDTO);

    MobileInfoVO getMobileinfo(Integer tenantId);
}
