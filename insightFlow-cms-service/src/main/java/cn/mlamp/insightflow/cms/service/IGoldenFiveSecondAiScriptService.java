package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.model.query.ActivityScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.PersonaGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.vo.ActivityScriptGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.PersonaGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.ScriptGenerationVO;

/**
 * 黄金5秒AI脚本生成服务接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IGoldenFiveSecondAiScriptService {

    /**
     * 生成人物画像
     *
     * @param request 人物画像生成请求
     * @return 人物画像生成响应
     */
    PersonaGenerationVO generatePersona(PersonaGenerationRequest request);

    /**
     * 生成活动逼单话术
     *
     * @param request 活动逼单话术生成请求
     * @return 活动逼单话术生成响应
     */
    ActivityScriptGenerationVO generateActivityScript(ActivityScriptGenerationRequest request);

    /**
     * 生成脚本
     *
     * @param request 脚本生成请求
     * @return 脚本生成响应
     */
    ScriptGenerationVO generateScript(ScriptGenerationRequest request);
}
