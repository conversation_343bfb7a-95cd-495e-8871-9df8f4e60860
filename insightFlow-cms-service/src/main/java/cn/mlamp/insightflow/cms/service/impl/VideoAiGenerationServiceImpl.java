package cn.mlamp.insightflow.cms.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.hutool.core.io.FileUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.constant.FileConstant;
import cn.mlamp.insightflow.cms.entity.CmsTaskDetail;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.entity.TenantToken;
import cn.mlamp.insightflow.cms.enums.TaskTypeEnum;
import cn.mlamp.insightflow.cms.enums.TokenTaskTypeEnum;
import cn.mlamp.insightflow.cms.enums.VideoTaskSourceTypeEnum;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.TenantTokenMapper;
import cn.mlamp.insightflow.cms.mapper.TokenUseDetailMapper;
import cn.mlamp.insightflow.cms.model.dto.VideoAiGenerateDTO;
import cn.mlamp.insightflow.cms.model.vo.VideoAiGenerateVO;
import cn.mlamp.insightflow.cms.service.FileService;
import cn.mlamp.insightflow.cms.service.ICmsTaskInfoService;
import cn.mlamp.insightflow.cms.service.TenantTokenService;
import cn.mlamp.insightflow.cms.service.TokenUseDetailService;
import cn.mlamp.insightflow.cms.service.VideoAiGenerationService;
import cn.mlamp.insightflow.cms.service.dam.IDamDirectoryService;
import cn.mlamp.insightflow.cms.strategy.handle.VideoAiGenerationHandle;
import cn.mlamp.insightflow.cms.util.FilePathBuilder;
import cn.mlamp.insightflow.cms.util.JsonUtil;
import cn.mlamp.insightflow.cms.util.PathUtil;
import static cn.mlamp.insightflow.cms.util.VideoUtil.convertImageToBase64;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class VideoAiGenerationServiceImpl implements VideoAiGenerationService {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IS3FlowService ossFileService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ICmsTaskInfoService ICmsTaskInfoService;

    @Autowired
    private CmsTaskDetailServiceImpl cmsTaskDetailServiceImpl;

    @Autowired
    private TokenUseDetailMapper tokenUseDetailMapper;

    @Autowired
    private UserServiceImpl userServiceImpl;

    @Autowired
    private VideoAiGenerationHandle VideoAiGenerationHandle;

    @Autowired
    private TokenUseDetailService tokenUseDetailService;

    @Autowired
    private TenantTokenService tenantTokenService;

    @Autowired
    private TenantTokenMapper tenantTokenMapper;

    @Resource(name = "cmsS3FlowService")
    private IS3FlowService cmsS3FlowService;

    @Resource
    private IDamDirectoryService directoryService;


    /**
     * 创建视频生成任务
     *
     * @param request  请求参数
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 任务创建响应
     */
    @Override
    public VideoAiGenerateVO.ViduCreateResponse createVideoTask(VideoAiGenerateDTO.ViduCreateRequest request,
            Integer userId, Integer tenantId) {
        // 参数校验
        validateRequest(request);

        // 获取用户昵称
        List<Integer> userIds = new ArrayList<>();
        userIds.add(userId);
        List<CmsUser> names = userServiceImpl.listByUserIds(userIds);
        String name = names != null ? names.get(0).getUserName() : "未知用户";

        // 创建任务记录
        CmsTaskInfo cmsTaskInfo = new CmsTaskInfo();
        cmsTaskInfo.setSourceType(VideoTaskSourceTypeEnum.UPLOAD.getCode()); // 设置来源类型，用户上传
        cmsTaskInfo.setName(name); // 设置昵称
        cmsTaskInfo.setTaskStatus(VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATING.getCode()); // 设置任务状态为视频生成中
        cmsTaskInfo.setTaskType(TaskTypeEnum.VIDEO_GENERATE.getCode()); // 设置任务类型为AI视频生成
        cmsTaskInfo.setUserId(userId); // 设置创建人ID
        cmsTaskInfo.setTenantId(tenantId); // 设置租户ID

        // 将request对象JSON化并存储到taskArg字段
        try {
            String requestJson = JsonUtil.encode(request);
            cmsTaskInfo.setTaskArg(requestJson);
        } catch (Exception e) {
            log.error("序列化request对象失败", e);
            throw new BusinessException("创建任务失败：参数序列化异常");
        }

        Date date = new Date();
        cmsTaskInfo.setCreateTime(date);
        ICmsTaskInfoService.save(cmsTaskInfo);

        // 获取任务ID
        Integer taskId = cmsTaskInfo.getId();
        String responseId = taskId.toString();

        // 创建响应对象
        VideoAiGenerateVO.ViduCreateResponse createResponse = new VideoAiGenerateVO.ViduCreateResponse(responseId,
                request.getOssId(), request.getTheme(), request.getStyle(), request.getEnvironment(),
                request.getCondition(), request.getFrequency(), date);

        return createResponse;
    }

    /**
     * 异步执行视频生成任务 使用并发方式同时创建多个视频
     *
     * @param request  请求参数
     * @param taskId   任务ID
     * @param userId   用户ID
     * @param tenantId 租户ID
     */
    @Async("commonTaskExecutor")
    @Override
    public void generateVideoAsync(VideoAiGenerateDTO.ViduCreateRequest request, String taskId, Integer userId,
            Integer tenantId) {
        try {
            log.info("开始异步执行视频生成任务，taskId={}，并发数量={}", taskId, request.getFrequency());

            // 检查租户余额是否足够
            int videoTokens = (int) 40 * 1000; // 每个视频消耗40000 tokens
            int totalRequiredTokens = videoTokens * request.getFrequency(); // 总需要的tokens

            // 查询租户当前余额
            LambdaQueryWrapper<TenantToken> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(TenantToken::getBalance).eq(TenantToken::getTenantId, tenantId);
            TenantToken token = tenantTokenMapper.selectOne(queryWrapper);

            if (token == null) {
                log.error("租户不存在，无法执行视频生成任务，tenantId={}", tenantId);
                updateTaskStatus(Integer.valueOf(taskId), VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATE_FAILED, userId, tenantId);
                throw new BusinessException("租户不存在，无法执行视频生成任务");
            }

            Integer balance = token.getBalance();
            Integer totalToken = tokenUseDetailMapper.selectTotalTokens(tenantId);
            Integer availableBalance = balance - totalToken;

            if (availableBalance < totalRequiredTokens) {
                log.error("租户余额不足，无法执行视频生成任务，tenantId={}，可用余额={}，需要余额={}",
                        tenantId, availableBalance, totalRequiredTokens);
                updateTaskStatus(Integer.valueOf(taskId), VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATE_FAILED, userId, tenantId);
                throw new BusinessException("租户余额不足，无法执行视频生成任务，请充值后再试");
            }

            log.info("租户余额检查通过，tenantId={}，可用余额={}，需要余额={}",
                    tenantId, availableBalance, totalRequiredTokens);

            // 创建线程池，用于并发生成视频
            int threadCount = Math.min(request.getFrequency(), 5); // 最多5个线程
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            // 并发生成视频
            for (int i = 0; i < request.getFrequency(); i++) {
                final int index = i;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        log.info("开始生成第{}个视频，taskId={}", index + 1, taskId);

                        // 构建prompt参数
                        String prompt = "主题" + ":" + request.getTheme() + "，" + "风格" + ":" + request.getStyle() + "，"
                                + "环境" + ":" + request.getEnvironment() + "，" + "要求" + ":" + request.getCondition();

                        // 构建Vidu请求体
                        VideoAiGenerationHandle.ViduGenerateRequest viduRequest = buildViduRequest(request,
                                request.getOssId(), prompt);

                        // 调用Vidu API
                        VideoAiGenerationHandle.ViduGenerateResponse response = VideoAiGenerationHandle
                                .generateVideo(viduRequest);

                        // 保存task detail
                        JSONObject json = new JSONObject();
                        json.put("viduId", response.getTaskId());
                        json.put("images", request.getOssId());
                        json.put("theme", request.getTheme());
                        json.put("style", request.getStyle());
                        json.put("environment", request.getEnvironment());
                        json.put("condition", request.getCondition());
                        json.put("status", "7");
                        Random random = new Random();
                        json.put("videoName", random.nextLong());

                        // 将JSON对象转换为字符串存储
                        String jsonString = json.toString();
                        synchronized (this) {
                            // 使用同步块保护数据库操作
                            cmsTaskDetailServiceImpl.saveTaskDetail(Integer.valueOf(taskId), 1, "4", jsonString);
                        }

                        // 扣减点数
                        var counttask = ICmsTaskInfoService.getById(Integer.valueOf(taskId));
                        if (response.getTaskId() != null) {
                            String filename = counttask.getName() != null ? counttask.getName()
                                    : counttask.getId().toString();
                            synchronized (this) {
                                // 使用同步块保护数据库操作
                                tokenUseDetailService.saveTokenDetail(videoTokens, response.getTaskId(),
                                        Integer.valueOf(taskId), TokenTaskTypeEnum.AI_IMITATE_TASK.getTaskType(),
                                        filename + "-vidu视频生成", tenantId, userId);
                            }
                        }

                        log.info("第{}个视频生成请求已发送，taskId={}，viduId={}", index + 1, taskId, response.getTaskId());
                    } catch (Exception e) {
                        log.error("第{}个视频生成请求失败，taskId={}", index + 1, taskId, e);
                    }
                }, executor);

                futures.add(future);
            }

            // 等待所有视频生成请求完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 关闭线程池
            executor.shutdown();

            log.info("所有视频生成请求已发送，开始轮询任务状态，taskId={}", taskId);

            // 所有请求发送完成后，开始轮询任务状态
            pollTaskStatus(taskId, userId, tenantId);

            log.info("所有视频生成任务异步执行完成，taskId={}", taskId);
        } catch (Exception e) {
            log.error("视频生成任务异步执行失败，taskId={}", taskId, e);
            // 更新任务状态为失败（使用VIDU_AI_VIDEO_ENTERSTOREHOUSE_FAILED状态）
            updateTaskStatus(Integer.valueOf(taskId), VideoTaskStatusEnum.VIDU_AI_VIDEO_ENTERSTOREHOUSE_FAILED, userId,
                    tenantId);
        }
    }

    /**
     * 轮询检查任务状态
     *
     * @param taskId   主任务ID
     * @param userId   用户ID
     * @param tenantId 租户ID
     */
    private void pollTaskStatus(String taskId, Integer userId, Integer tenantId) {
        try {
            // 最多轮询30次，每次间隔10秒
            int maxRetries = 120;
            int successCount = 0;
            int failedCount = 0;

            for (int i = 0; i < maxRetries; i++) {
                log.info("开始轮询所有视频任务状态，taskId={}", taskId);
                // 获取任务详情
                List<CmsTaskDetail> cmsTaskDetails = cmsTaskDetailServiceImpl.getTaskResult(Integer.valueOf(taskId));
                if (cmsTaskDetails == null || cmsTaskDetails.isEmpty()) {
                    log.warn("任务详情不存在，无法轮询状态，taskId={}", taskId);
                    return;
                }

                // 检查所有视频任务的状态
                for (int j = 0; j < cmsTaskDetails.size(); j++) {
                    try {
                        CmsTaskDetail detail = cmsTaskDetails.get(j);
                        JSONObject data = JSONObject.parseObject(detail.getData());
                        String viduId = data.getString("viduId");

                        // 如果已经处理过（有videoOssId或标记为失败），则跳过
                        if (!StringUtils.isEmpty(data.getString("videoOssId"))
                                || "9".equals(data.getString("status"))) {
                            continue;
                        }

                        // 查询Vidu任务状态
                        VideoAiGenerationHandle.TaskStatusResponse taskStatusResponse = VideoAiGenerationHandle
                                .queryTaskStatus(viduId);
                        String state = taskStatusResponse.getState();
                        log.info("轮询任务状态，taskId={}，viduId={}，state={}，第{}次", taskId, viduId, state, i + 1);

                        // 如果任务已完成，处理结果
                        if ("success".equals(state)) {
                            // 下载视频并上传OSS
                            String videoOssId = downloadAndUploadToOss(
                                    taskStatusResponse.getCreations().get(0).getUrl());
                            // 下载封面并上传OSS
                            String coverOssId = downloadAndUploadToOss(
                                    taskStatusResponse.getCreations().get(0).getCover_url());

                            // 保存视频和封面的ossId
                            data.put("videoOssId", videoOssId);
                            data.put("coverOssId", coverOssId);
                            data.put("status", "8");
                            detail.setData(data.toString());
                            detail.setUpdateTime(new Date());
                            cmsTaskDetailServiceImpl.updateById(detail);

                            successCount++;
                        } else if ("failed".equals(state)) {
                            // 任务失败
                            log.error("视频生成任务失败，taskId={}，viduId={}", taskId, viduId);

                            // 保存失败状态
                            data.put("status", "9"); // 9表示失败
                            data.put("err_code", taskStatusResponse.getErr_code()); // 9表示失败
                            detail.setData(data.toString());
                            detail.setUpdateTime(new Date());
                            cmsTaskDetailServiceImpl.updateById(detail);

                            failedCount++;
                        }
                    } catch (Exception e) {
                        log.error("处理单个视频任务状态异常，taskId={}，index={}", taskId, j, e);
                    }
                }

                log.info("轮询进度：taskId={}，成功={}，失败={}，总数={}", taskId, successCount, failedCount, cmsTaskDetails.size());

                // 如果所有任务都已完成（成功或失败），更新主任务状态并退出
                if (successCount + failedCount >= cmsTaskDetails.size()) {
                    if (failedCount == 0) {
                        // 全部成功
                        updateTaskStatus(Integer.valueOf(taskId),
                                VideoTaskStatusEnum.VIDU_AI_VIDEO_ENTERSTOREHOUSE_WAIT, userId, tenantId);
                        log.info("所有视频任务已成功完成，taskId={}", taskId);
                    } else if (successCount == 0) {
                        // 全部失败
                        updateTaskStatus(Integer.valueOf(taskId),
                                VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATE_FAILED, userId, tenantId);
                        log.info("所有视频任务均失败，taskId={}", taskId);
                    } else {
                        // 部分成功，部分失败
                        updateTaskStatus(Integer.valueOf(taskId),
                                VideoTaskStatusEnum.VIDU_AI_VIDEO_ENTERSTOREHOUSE_WAIT, userId, tenantId);
                        log.info("部分视频任务成功完成，部分失败，taskId={}，成功={}，失败={}", taskId, successCount, failedCount);
                    }
                    return;
                }

                // 如果还有任务未完成，等待后继续轮询
                Thread.sleep(5000); // 等待5秒
            }

            // 超过最大轮询次数，更新任务状态
            log.warn("轮询任务状态超时，taskId={}", taskId);
            updateTaskStatus(Integer.valueOf(taskId), VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATE_FAILED, userId,
                    tenantId);
        } catch (Exception e) {
            log.error("轮询任务状态失败，taskId={}", taskId, e);
            try {
                updateTaskStatus(Integer.valueOf(taskId), VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATE_FAILED,
                        userId, tenantId);
            } catch (Exception ex) {
                log.error("更新任务状态失败，taskId={}", taskId, ex);
            }
        }
    }

    /**
     * 查询视频生成任务状态 只返回本地数据库中的任务信息，不再请求Vidu API
     *
     * @param taskId 任务ID
     * @return 查询响应
     */
    @Override
    public VideoAiGenerateVO.ViduQueryResponse queryVideo(String taskId) {
        VideoAiGenerateVO.ViduQueryResponse response = new VideoAiGenerateVO.ViduQueryResponse();

        try {
            // 获取任务信息
            CmsTaskInfo taskInfo = ICmsTaskInfoService.getById(Integer.valueOf(taskId));
            if (taskInfo == null) {
                log.warn("任务不存在，taskId={}", taskId);
                response.setStatus("not_found");
                return response;
            }

            // 根据任务状态设置响应状态
            if (taskInfo.getTaskStatus() == VideoTaskStatusEnum.VIDU_AI_VIDEO_ENTERSTOREHOUSE_WAIT.getCode()) {
                response.setStatus("success");

                // 获取任务详情
                List<CmsTaskDetail> cmsTaskDetails = cmsTaskDetailServiceImpl.getTaskResult(Integer.valueOf(taskId));
                if (cmsTaskDetails == null || cmsTaskDetails.isEmpty()) {
                    log.warn("任务详情不存在，taskId={}", taskId);
                    response.setStatus("not_found");
                    return response;
                }

                // 解析任务详情
                JSONObject parsedJson = JSONObject.parseObject(cmsTaskDetails.get(0).getData());
                List<String> images = parsedJson.getJSONArray("images").toJavaList(String.class);
                String theme = parsedJson.getString("theme");
                String style = parsedJson.getString("style");
                String environment = parsedJson.getString("environment");
                String condition = parsedJson.getString("condition");
                String status = parsedJson.getString("status");

                // 构建响应数据
                List<VideoAiGenerateVO.OssidAndUrl> ossidAndUrls = buildOssidAndUrlList(cmsTaskDetails);
                Date updateTime = taskInfo.getUpdateTime() != null ? taskInfo.getUpdateTime() : new Date();

                // 设置响应数据
                VideoAiGenerateVO.VideoData videoData = new VideoAiGenerateVO.VideoData(taskId, images, theme, style,
                        environment, condition, updateTime, status, ossidAndUrls);
                response.setVideoData(videoData);

            } else if (taskInfo.getTaskStatus() == VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATE_FAILED.getCode()) {
                response.setStatus("failed");
                // 其他情况返回空数据
                response.setVideoData(null);
            } else if (taskInfo.getTaskStatus() == VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATING.getCode()) {
                response.setStatus("processing");
                // 其他情况返回空数据
                response.setVideoData(null);
            } else {
                response.setStatus("unknown");
                // 其他情况返回空数据
                response.setVideoData(null);
            }

            return response;
        } catch (Exception e) {
            log.error("查询视频生成任务状态失败，taskId={}", taskId, e);
            response.setStatus("error");
            return response;
        }
    }


    /**
     * 构建OssidAndUrl列表
     */
    private List<VideoAiGenerateVO.OssidAndUrl> buildOssidAndUrlList(List<CmsTaskDetail> cmsTaskDetails) {
        List<VideoAiGenerateVO.OssidAndUrl> ossidAndUrls = new ArrayList<>();

        for (CmsTaskDetail detail : cmsTaskDetails) {
            try {
                JSONObject data = JSONObject.parseObject(detail.getData());
                String videoOssId = data.getString("videoOssId");
                String coverOssId = data.getString("coverOssId");

                // 如果已经有视频和封面ID，添加到列表
                if (!StringUtils.isEmpty(videoOssId) && !StringUtils.isEmpty(coverOssId)) {
                    VideoAiGenerateVO.OssidAndUrl ossidAndUrl = new VideoAiGenerateVO.OssidAndUrl(coverOssId,
                            videoOssId, fileService.getPicDownloadSignatureUrl(coverOssId),
                            fileService.getPicDownloadSignatureUrl(videoOssId), detail.getId());
                    ossidAndUrls.add(ossidAndUrl);
                }
            } catch (Exception e) {
                log.warn("解析任务详情失败，detailId={}", detail.getId(), e);
            }
        }

        return ossidAndUrls;
    }


    /**
     * 分页查询视频生成任务列表
     *
     * @param actualLimit 每页数量
     * @param actualPage  页码
     * @param userId      用户ID
     * @return 任务列表响应
     */
    @Override
    public VideoAiGenerateVO.ViduListResponse queryListVideo(int actualLimit, int actualPage, Integer userId) {
        // 1. 分页查询任务列表
        Page<CmsTaskInfo> page = new Page<>(actualPage, actualLimit);
        LambdaQueryWrapper<CmsTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsTaskInfo::getUserId, userId)
                .eq(CmsTaskInfo::getTaskType, TaskTypeEnum.VIDEO_GENERATE.getCode()) // 只查询视频生成任务
                .orderByDesc(CmsTaskInfo::getCreateTime);
        Page<CmsTaskInfo> taskPage = ICmsTaskInfoService.page(page, queryWrapper);
        Long total = taskPage.getTotal();

        List<VideoAiGenerateVO.VideoListData> videoListDatas = taskPage.getRecords().stream().map(task -> {
            VideoAiGenerateVO.VideoListData videoListData = new VideoAiGenerateVO.VideoListData();

            // 设置任务状态
            if (task.getTaskStatus() == VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATING.getCode()) {
                videoListData.setStatus(VideoTaskStatusEnum.VIDU_AI_VIDEO_GENERATING.getCode());

                // 对于生成中的任务，只返回基本信息
                videoListData.setTaskId(task.getId().toString());
                videoListData.setCreateTime(task.getCreateTime());
                videoListData.setName(Optional.ofNullable(task.getName()).orElse("未知用户"));
                videoListData.setVideoList(List.of());
                videoListData.setCount("0");

                return videoListData;
            } else {
                videoListData.setStatus(task.getTaskStatus());
            }

            // 基础信息
            videoListData.setTaskId(task.getId().toString());
            videoListData.setCreateTime(task.getCreateTime());
            videoListData.setName(Optional.ofNullable(task.getName()).orElse("未知用户"));

            // 查询关联任务详情
            List<CmsTaskDetail> details = cmsTaskDetailServiceImpl.getTaskResult(task.getId());
            if (details == null || details.isEmpty()) {
                videoListData.setVideoList(List.of());
                videoListData.setCount("0");
                return videoListData;
            }

            // 处理视频列表和点数计算
            List<VideoAiGenerateVO.VideoItem> videoList = details.stream()
                    .filter(detail -> detail != null && detail.getData() != null).map(detail -> {
                        try {
                            JSONObject data = JSONObject.parseObject(detail.getData());
                            if (data == null) {
                                return null;
                            }

                            // 提取 ossId 并判断是否为空
                            String videoOssId = data.getString("videoOssId");
                            String coverOssId = data.getString("coverOssId");

                            // 如果视频或封面ID为空，则跳过
                            if (StringUtils.isEmpty(videoOssId) || StringUtils.isEmpty(coverOssId)) {
                                return null;
                            }

                            return new VideoAiGenerateVO.VideoItem(
                                    // 处理 videoOssId 签名URL
                                    fileService.getPicDownloadSignatureUrl(videoOssId),
                                    // 处理 coverOssId 签名URL
                                    fileService.getPicDownloadSignatureUrl(coverOssId),
                                    // 原始 ossId 字段
                                    videoOssId, coverOssId, data.getString("videoName"));
                        } catch (Exception e) {
                            log.warn("解析任务详情数据失败，detailId={}", detail.getId(), e);
                            return null;
                        }
                    }).filter(item -> item != null).collect(Collectors.toList());

            videoListData.setVideoList(videoList);

            // 计算消耗的点数（每个视频消耗40点数，即40000token）
            int tokenPoints = videoList.size() * 40;
            videoListData.setCount(String.valueOf(tokenPoints));

            return videoListData;
        }).collect(Collectors.toList());

        // 构建响应结构
        VideoAiGenerateVO.ViduListResponse response = new VideoAiGenerateVO.ViduListResponse();
        response.setListData(videoListDatas);
        response.setTotal(total);
        response.setPageSize(actualLimit);
        response.setCurrentpage(actualPage);
        response.setStatus("success");

        return response;
    }

    /**
     * 格式化日期时间
     */
    private Date formatDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date;
    }

    //  参数校验
    private void validateRequest(VideoAiGenerateDTO.ViduCreateRequest request) {
        if (request.getOssId() == null || request.getOssId().size() < 1 || request.getOssId().size() > 3) {
            throw new BusinessException("需提供1-3张参考图片");
        }
        if (request.getTheme() == null || request.getTheme().isEmpty()) {
            throw new BusinessException("主题不能为空");
        }
        if (request.getStyle() == null || request.getStyle().isEmpty()) {
            throw new BusinessException("风格不能为空");
        }
        if (request.getEnvironment() == null || request.getEnvironment().isEmpty()) {
            throw new BusinessException("环境不能为空");
        }
        if (request.getCondition() == null || request.getCondition().isEmpty()) {
            throw new BusinessException("条件不能为空");
        }
        if (request.getDuration() == null || request.getDuration() < 1) {
            throw new BusinessException("视频时长必须大于0");
        }
        if (request.getMovementAmplitude() == null
                || !Arrays.asList("auto", "small", "medium", "large").contains(request.getMovementAmplitude())) {
            throw new BusinessException("运动幅度参数不合法");
        }
        if (request.getAspectRatio() == null
                || !Arrays.asList("9:16", "16:9", "1:1").contains(request.getAspectRatio())) {
            throw new BusinessException("宽高比参数不合法");
        }
        if (request.getResolution() == null
                || !Arrays.asList("360p", "720p").contains(request.getResolution())) {
            throw new BusinessException("分辨率参数不合法");
        }
        if (request.getFrequency() > 4 || request.getFrequency() < 1) {
            throw new BusinessException("次数必须在一次到四次间");
        }

    }

    //  构建Vidu创建任务请求
    private VideoAiGenerationHandle.ViduGenerateRequest buildViduRequest(VideoAiGenerateDTO.ViduCreateRequest request,
            List<String> ossIds, String prompt) {
        // request.setAspectRatio("9:16");

        VideoAiGenerationHandle.ViduGenerateRequest viduRequest = new VideoAiGenerationHandle.ViduGenerateRequest();
        viduRequest.setModel("vidu2.0");
        viduRequest.setImages(getImageBase64List(ossIds));
        viduRequest.setPrompt(prompt);
        viduRequest.setDuration(request.getDuration());
        viduRequest.setAspectRatio(request.getAspectRatio());
        viduRequest.setResolution(request.getResolution());
        viduRequest.setMovementAmplitude(request.getMovementAmplitude());

        return viduRequest;
    }


    // 通过ossid获取图片并转化为Base64编码
    public List<String> getImageBase64List(List<String> ossIds) {
        List<String> base64List = new ArrayList<>();

        for (String ossId : ossIds) {
            Path tempFile = null;
            try {
                // 1. 获取预签名URL
                String url = fileService.getPicDownloadSignatureUrl(ossId);

                // 2. 创建临时文件
                tempFile = Files.createTempFile("img_", ".tmp");

                // 3. 下载图片到临时文件
                try (InputStream in = new URL(url).openStream()) {
                    Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
                }

                // 4. 转换为Base64，添加vidu接口前缀，加入列表
                base64List.add("data:image/png;base64," + convertImageToBase64(tempFile.toString()));

            } catch (Exception e) {
                log.error("处理OSSID: {} 时发生异常", ossId, e);
                throw new BusinessException("图片处理失败: " + ossId);
            } finally {
                // 5. 清理临时文件
                if (tempFile != null) {
                    try {
                        Files.deleteIfExists(tempFile);
                    } catch (IOException ex) {
                        log.warn("临时文件删除失败: {}", tempFile, ex);
                    }
                }
            }
        }
        return base64List;
    }

    // 解析Vidu API返回的JSON数据
    private VideoAiGenerationHandle.ViduGenerateResponse parseViduResponse(String responseBody) {
        JSONObject json = JSONObject.parseObject(responseBody);

        VideoAiGenerationHandle.ViduGenerateResponse response = new VideoAiGenerationHandle.ViduGenerateResponse();
        response.setTaskId(json.getString("task_id"));
        response.setState(json.getString("state"));
        response.setModel(json.getString("model"));
        response.setImages(json.getJSONArray("images").toJavaList(String.class));
        response.setPrompt(json.getString("prompt"));
        response.setDuration(json.getInteger("duration"));
        response.setSeed(json.getInteger("seed"));
        response.setAspectRatio(json.getString("aspect_ratio"));
        response.setResolution(json.getString("resolution"));
        response.setMovementAmplitude(json.getString("movement_amplitude"));
        response.setCreatedAt(json.getString("created_at"));

        return response;
    }

    @Override
    public Integer getUploadDirectoryId(Integer userId, Integer tenantId) {
        return directoryService.getAiVideoUploadDictionaryId(userId, tenantId);
    }

    @Override
    public int updateTaskStatus(Integer taskId, VideoTaskStatusEnum status, Integer userId, Integer tenantId) {
        return ICmsTaskInfoService.updateVideoTaskStatus(status, "", taskId, userId, tenantId);
    }

    private String downloadAndUploadToOss(String fileUrl) throws IOException {
        String fileExtension = getFileUniqueIdentifier(fileUrl);
        String localFilePath = System.getProperty("user.dir") + "/" + fileExtension;

        try {
            // 下载文件到本地临时路径
            try (InputStream in = new URL(fileUrl).openStream();
                 FileOutputStream fos = new FileOutputStream(localFilePath)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }

            // 生成OSS ID（关键路径）
            String ossId = FilePathBuilder.generateImageOssPath(
                    FileConstant.OSS_PATH_AI_VIDEO_PREFIX,
                    PathUtil.pathToSuffix(localFilePath)
            );

            // 上传到OSS
            cmsS3FlowService.upload(ossId, new File(localFilePath));
            return ossId; // 直接返回OSS对象ID


        } finally {
            FileUtil.del(localFilePath); // 清理临时文件
        }
    }

    private String getFileUniqueIdentifier(String url) {
        try {
            // 解析URL并提取路径部分（忽略查询参数）
            URL parsedUrl = new URL(url);
            String path = parsedUrl.getPath();

            // 计算路径的MD5哈希值（唯一性保障）
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(path.getBytes(StandardCharsets.UTF_8));
            StringBuilder hashHex = new StringBuilder();
            for (byte b : hashBytes) {
                hashHex.append(String.format("%02x", b));
            }
            String uniqueHash = hashHex.toString();

            // 提取文件扩展名
            int dotIndex = path.lastIndexOf('.');
            String extension = (dotIndex == -1) ? ".tmp" : path.substring(dotIndex);

            // 组合哈希值 + 扩展名
            return uniqueHash + extension;

        } catch (MalformedURLException e) {
            return "invalid_url.tmp"; // URL格式错误兜底
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not available", e);
        }
    }


}



