package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

/**
 * 文件的状态信息
 */


@Getter
public enum TaskTypeEnum {

    /**
     * 套路复用
     */
    SCRIPT_REUSE(0, "套路复用"),

    /**
     * AI仿写
     */
    AI_WRITE(1, "AI仿写"),

    /**
     * 帖子打标
     */
    POST_TAG(2, "帖子打标"),

    /**
     * 视频打标
     */
    VIDEO_TAG(3, "素材AI打标"),

    /**
     * 视频生成
     */
    VIDEO_GENERATE(4, "ai视频生成"),

    /**
     * 视频合成
     */
    VIDEO_SYNTHESIS(5, "视频合成"),

            /**
     * 黄金3秒AI仿写
     */
    GOLDEN_FIVE_SECOND_AI_WRITE(6, "黄金3秒AI仿写"),
    ;

    private final int code;
    private final String msg;

    TaskTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static TaskTypeEnum getByCode(int code) {
        for (TaskTypeEnum type : TaskTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
