package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.adapter.EsAdapter;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.enums.TaskTypeEnum;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.query.ActivityScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.PersonaGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.model.vo.ActivityScriptGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.PersonaGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.ScriptGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.UserStoryVO;
import cn.mlamp.insightflow.cms.service.ICmsProductRecordService;
import cn.mlamp.insightflow.cms.service.ICmsTaskInfoService;
import cn.mlamp.insightflow.cms.service.IGoldenFiveSecondAiScriptService;
import cn.mlamp.insightflow.cms.service.TenantTokenService;
import cn.mlamp.insightflow.cms.service.webflux.DifyRequestService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 黄金5秒AI脚本生成服务实现
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Slf4j
@Service
public class GoldenFiveSecondAiScriptServiceImpl implements IGoldenFiveSecondAiScriptService {

    @Autowired
    private EsAdapter esAdapter;

    @Autowired
    private DifyRequestService difyRequestService;

    @Autowired
    private ICmsTaskInfoService taskInfoService;

    @Autowired
    private TenantTokenService tenantTokenService;

    @Autowired
    private ICmsProductRecordService productRecordService;

    @Override
    public PersonaGenerationVO generatePersona(PersonaGenerationRequest request) {
        try {
            // 第一步：ES拉取社交媒体数据
            String socialMediaData = fetchSocialMediaData(request);

            // 第二步：调用Dify工作流生成人物画像
            String personaResult = callDifyPersonaGeneration(request, socialMediaData);

            // 第三步：解析Dify返回的用户故事
            List<UserStoryVO> userStories = parsePersonaResult(personaResult);

            // 构建返回结果
            PersonaGenerationVO result = new PersonaGenerationVO();
            result.setSocialMediaData(socialMediaData);
            result.setPersonaResult(userStories);
            result.setBrand(request.getBrand());
            result.setProduct(request.getProduct());
            result.setSellingPoint(request.getSellingPoint());

            return result;

        } catch (Exception e) {
            log.error("生成人物画像失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成人物画像失败: " + e.getMessage());
        }
    }

    @Override
    public ActivityScriptGenerationVO generateActivityScript(ActivityScriptGenerationRequest request) {
        try {
            // 调用Dify工作流生成活动逼单话术
            String scriptText = difyRequestService.activityScriptGeneration(request.getActivities());

            // 构建返回结果
            ActivityScriptGenerationVO result = new ActivityScriptGenerationVO();
            result.setScriptText(scriptText);

            return result;

        } catch (Exception e) {
            log.error("生成活动逼单话术失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成活动逼单话术失败: " + e.getMessage());
        }
    }

    @Override
    public ScriptGenerationVO generateScript(ScriptGenerationRequest request) {

        // 检查租户点数
        if (tenantTokenService.checkBalance(request.getTenantId()) <= 0) {
            throw new RuntimeException("租户点数不足");
        }

        // 创建任务记录
        Integer taskId = createScriptTask(request, request.getUserId(), request.getTenantId());

        // 返回任务ID
        ScriptGenerationVO result = new ScriptGenerationVO();
        result.setTaskId(taskId.toString());
        return result;
    }

    /**
     * ES拉取社交媒体数据
     */
    private String fetchSocialMediaData(PersonaGenerationRequest request) {
        try {
            // 生成索引列表
            String[] indices = generateIndexList();

            // 构建查询条件
            BoolQueryBuilder boolQuery = buildPersonaQuery(request);

            // 构建搜索请求
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQuery);
            sourceBuilder.size(10);
            sourceBuilder.sort("long_interactCount", SortOrder.DESC);

            SearchRequest searchRequest = new SearchRequest(indices);
            searchRequest.source(sourceBuilder);

            // 执行搜索
            SearchResponse searchResponse = esAdapter.es7Client.search(searchRequest, RequestOptions.DEFAULT);

            // 处理搜索结果，转换为字符串格式
            return convertSearchResponseToString(searchResponse);

        } catch (Exception e) {
            log.error("ES拉取社交媒体数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("ES拉取社交媒体数据失败: " + e.getMessage());
        }
    }

    /**
     * 调用Dify工作流生成人物画像
     */
    private String callDifyPersonaGeneration(PersonaGenerationRequest request, String posts) {
        try {
            return difyRequestService.personaGeneration(posts, request.getBrand(), request.getProduct(),
                    request.getSellingPoint());
        } catch (Exception e) {
            log.error("调用Dify人物画像生成失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用Dify人物画像生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成索引列表 根据近一个月的数据，生成4个索引：当月和上月的weibo和share索引
     */
    private String[] generateIndexList() {
        LocalDate now = LocalDate.now();
        LocalDate lastMonth = now.minusMonths(1);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        String currentMonth = now.format(formatter);
        String previousMonth = lastMonth.format(formatter);

        List<String> indexList = new ArrayList<>();
        indexList.add("ik_sl_v2_" + currentMonth + "_weibo");
        indexList.add("ik_sl_v2_" + currentMonth + "_share");
        indexList.add("ik_sl_v2_" + previousMonth + "_weibo");
        indexList.add("ik_sl_v2_" + previousMonth + "_share");

        log.info("生成的索引列表: {}", indexList);
        return indexList.toArray(new String[0]);
    }

    /**
     * 构建人物画像查询条件
     */
    private BoolQueryBuilder buildPersonaQuery(PersonaGenerationRequest request) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 构建filter条件
        BoolQueryBuilder filterQuery = QueryBuilders.boolQuery();

        // 添加must_not条件
        filterQuery.mustNot(QueryBuilders.existsQuery("kw_tagMarketing"));
        filterQuery.mustNot(QueryBuilders.existsQuery("kw_tagServiceInformation"));
        filterQuery.mustNot(QueryBuilders.existsQuery("kw_tagLowQualityContent"));

        // 添加range和terms条件
        filterQuery.must(QueryBuilders.rangeQuery("long_userLanguage").gte(10));
        filterQuery.must(QueryBuilders.termsQuery("kw_commonSentimentPlus", "正面", "中性"));
        filterQuery.must(QueryBuilders.termQuery("kw_docType", "1"));
        filterQuery.must(QueryBuilders.termsQuery("kw_tagPUGC", "UGC"));
        filterQuery.must(QueryBuilders.termsQuery("kw_source", "xiaohongshu.com", "weibo.com"));
        filterQuery.must(QueryBuilders.rangeQuery("date_publishedAt").gte("now-30d/d").lte("now/d"));

        boolQuery.filter(filterQuery);

        // 构建must查询条件
        String queryString = String.format("(\"%s\") AND (\"%s\")", request.getBrand(), request.getProduct());
        boolQuery.must(QueryBuilders.queryStringQuery(queryString).analyzer("ik_smart").field("text_content")
                .field("text_title").field("text_subtitle").field("object_imageInfo.text_content"));

        return boolQuery;
    }

    /**
     * 将搜索响应转换为字符串格式 格式：帖子1 标题：xxx 内容：xxx
     */
    private String convertSearchResponseToString(SearchResponse searchResponse) {
        StringBuilder result = new StringBuilder();
        SearchHit[] hits = searchResponse.getHits().getHits();

        for (int i = 0; i < hits.length; i++) {
            SearchHit hit = hits[i];
            Map<String, Object> sourceMap = hit.getSourceAsMap();

            String title = (String) sourceMap.get("text_title");
            String content = (String) sourceMap.get("text_content");

            result.append("帖子").append(i + 1).append(" 标题：").append(title != null ? title : "").append(" 内容：")
                    .append(content != null ? content : "");

            if (i < hits.length - 1) {
                result.append("\n");
            }
        }

        log.info("ES查询结果转换完成，共{}条记录", hits.length);
        return result.toString();
    }

    /**
     * 解析Dify返回的人物画像结果
     *
     * @param personaResult Dify返回的原始字符串
     * @return 解析后的用户故事列表
     */
    private List<UserStoryVO> parsePersonaResult(String personaResult) {
        List<UserStoryVO> userStories = new ArrayList<>();

        if (personaResult == null || personaResult.trim().isEmpty()) {
            log.warn("Dify返回结果为空");
            return userStories;
        }

        try {
            // 使用正则表达式匹配用户故事
            Pattern storyPattern = Pattern.compile("用户故事\\d+\\s*\\n([\\s\\S]*?)(?=用户故事\\d+|$)");
            Matcher storyMatcher = storyPattern.matcher(personaResult);

            while (storyMatcher.find()) {
                String storyContent = storyMatcher.group(1).trim();
                UserStoryVO userStory = parseUserStory(storyContent);
                if (userStory != null) {
                    userStories.add(userStory);
                }
            }

            log.info("成功解析{}个用户故事", userStories.size());

        } catch (Exception e) {
            log.error("解析Dify返回结果失败: {}", e.getMessage(), e);
        }

        return userStories;
    }

    /**
     * 解析单个用户故事
     *
     * @param storyContent 用户故事内容
     * @return 用户故事VO
     */
    private UserStoryVO parseUserStory(String storyContent) {
        try {
            UserStoryVO userStory = new UserStoryVO();

            // 解析具体人设
            String persona = extractField(storyContent, "具体人设：");
            userStory.setPersona(persona);

            // 解析对应生活化场景
            String lifeScene = extractField(storyContent, "对应生活化场景：");
            userStory.setLifeScene(lifeScene);

            // 解析对应痛点
            String painPoint = extractField(storyContent, "对应痛点：");
            userStory.setPainPoint(painPoint);

            // 解析对应产品卖点
            String productSellingPoint = extractField(storyContent, "对应产品卖点：");
            userStory.setProductSellingPoint(productSellingPoint);

            // 解析对应通用金句
            String slogan = extractField(storyContent, "对应通用金句：");
            userStory.setSlogan(slogan);

            // 解析对应产品昵称
            String productNickname = extractField(storyContent, "对应产品昵称：");
            userStory.setProductNickname(productNickname);

            return userStory;

        } catch (Exception e) {
            log.error("解析用户故事失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从文本中提取指定字段的值
     *
     * @param content   文本内容
     * @param fieldName 字段名称
     * @return 字段值
     */
    private String extractField(String content, String fieldName) {
        try {
            Pattern pattern = Pattern.compile(fieldName + "([^；\\n]*?)(?=；|\\n|$)");
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()) {
                return matcher.group(1).trim();
            }
        } catch (Exception e) {
            log.warn("提取字段{}失败: {}", fieldName, e.getMessage());
        }
        return "";
    }

    /**
     * 创建脚本生成任务
     */
    private Integer createScriptTask(ScriptGenerationRequest request, Integer userId, Integer tenantId) {
        var task = new CmsTaskInfo();
        task.setTaskType(TaskTypeEnum.GOLDEN_FIVE_SECOND_AI_WRITE.getCode());

        // 脚本标题 = 品牌_产品_卖点_时间戳
        String name = request.getBrand() + "_" + request.getProduct() + "_" + request.getSellingPoint() + "_"
                + DateUtil.format(new java.util.Date(), "yyyyMMddHHmmss");
        task.setName(name);
        task.setTaskStatus(VideoTaskStatusEnum.QUEUING.getCode());
        task.setTaskArg(JSONUtil.toJsonStr(request));
        task.setUserId(userId);
        task.setTenantId(tenantId);

        taskInfoService.save(task);

        // 保存商品信息
        VideoScriptGenRequest.ScriptInputContent input = new VideoScriptGenRequest.ScriptInputContent();
        input.setBrand(request.getBrand());
        input.setProduct(request.getProduct());
        input.setSellingPoint(request.getSellingPoint());
        productRecordService.saveByScriptInput(input, request.getUserId(), request.getTenantId());

        return task.getId();
    }

}
