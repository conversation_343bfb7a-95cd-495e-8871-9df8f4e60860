package cn.mlamp.insightflow.cms.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-07
 */
@Component
public class TaskConfig {

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Value("${cms.task.user:false}")
    private boolean userEnable;


    @Value("${cms.task.common:false}")
    private boolean commonEnable;

    @Value("${cms.task.num:15}")
    private int num;



    public boolean isLocal() {
        return "local".equals(activeProfiles);
    }

    public boolean isUserEnable() {
        return userEnable;
    }

    public boolean isCommonEnable() {
        return commonEnable;
    }

    public int getNum() {
        return num;
    }
}
