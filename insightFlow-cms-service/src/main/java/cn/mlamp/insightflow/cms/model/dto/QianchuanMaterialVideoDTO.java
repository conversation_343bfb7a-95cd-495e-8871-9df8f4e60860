package cn.mlamp.insightflow.cms.model.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import lombok.Data;

@Data
public class QianchuanMaterialVideoDTO {
    private String ossid;
    private String videoId;
    private String consumeRange;
    private String title;
    private String brand;
    private Integer exposure;
    private Integer shares;
    private Integer comments;
    private Integer likes;
    private Integer clicks;
    private String industry;
    private String ourIndustry; // 自定义行业分类
    private String rankingType;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") // 处理前端传来的 ISO 8601 字符串
    private LocalDateTime publishTime;
    private String authorName;
    private String authorAvatar;
    private String coverImage;
    private Integer duration;
    private Integer ourDuration; // 自定义视频时长（单位：秒）

    @DecimalMin("0.00")
    @DecimalMax("100.00")
    private BigDecimal completionRate;

    @DecimalMin("0.00")
    @DecimalMax("100.00")
    private BigDecimal ctr; // 点击率，百分比
}
