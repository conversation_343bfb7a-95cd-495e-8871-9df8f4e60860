package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.config.properties.UploadLinkProperties;
import cn.mlamp.insightflow.cms.entity.CmsProductRecord;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.CmsProductRecordMapper;
import cn.mlamp.insightflow.cms.model.dto.DifyProductSummaryResponseDTO;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.UploadFileVo;
import cn.mlamp.insightflow.cms.model.vo.UploadProductVO;
import cn.mlamp.insightflow.cms.service.ICmsProductRecordService;
import cn.mlamp.insightflow.cms.service.webflux.DifyRequestService;
import cn.mlamp.insightflow.cms.service.webflux.WebClientService;
import cn.mlamp.insightflow.cms.util.PageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * @description 针对表【cms_product_record(商品记录表)】的数据库操作Service实现
 */
@Service
@AllArgsConstructor
public class CmsProductRecordServiceImpl extends ServiceImpl<CmsProductRecordMapper, CmsProductRecord>
        implements ICmsProductRecordService {

    private final WebClientService webClientService;

    private final DifyRequestService difyRequestService;

    private final UploadLinkProperties uploadLinkProperties;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public UploadProductVO videoTaskProductUrl(TaskUploadVO uploadVO, Integer userId, Integer tenantId) {
        List<String> productUrls = uploadLinkProperties.getProductUrls();
        if (!checkLinkFormat(productUrls, uploadVO.getUploadUrl())) {
            throw new BusinessException("链接格式错误");
        }
        var result = new UploadProductVO();
        try {
            // 调用爬虫接口，获取商品信息
            var productInfo = webClientService.getTikTokSkuInfo(uploadVO.getUploadUrl()).block();
            if (productInfo == null || productInfo.getTitle() == null) {
                log.error("调用抓取接口失败");
                throw new BusinessException("调用抓取接口失败");
            }
            result.setTitle(productInfo.getTitle());
            // 调用dify总结商品信息
            var productStr = JSONUtil.toJsonStr(productInfo);
            // 获取信息存入数据库
            String difyResponse = difyRequestService.productInfoSummary(productStr);
            var product = DifyProductSummaryResponseDTO.buildByText(difyResponse);
            if (product == null || product.getProductName() == null) {
                log.error("调用dify接口总结商品失败");
                throw new BusinessException("调用dify接口总结商品失败");
            }
            this.saveByDto(product, userId, tenantId);
            result.setBrand(product.getBrand());
            result.setProductName(product.getProductName());
            result.setSellingPoint(product.getSellingPoint());
        } catch (Exception e) {
            throw new BusinessException("获取商品信息失败");
        }
        return result;
    }


    @Override
    public UploadFileVo videoTaskUploadProductFile(MultipartFile file,Integer userId, Integer tenantId) {
        var result = new UploadFileVo();
        try {
            // 获取信息存入数据库
            var difyResult = difyRequestService.productFileInfoSummary(file);
            var product = DifyProductSummaryResponseDTO.buildByText(difyResult);
            this.saveByDto(product, userId, tenantId);
            result.setBrand(product.getBrand());
            result.setProductName(product.getProductName());
            result.setSellingPoint(product.getSellingPoint());
        } catch (Exception e) {
            throw new BusinessException("获取商品信息失败");
        }
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveByScriptInput(VideoScriptGenRequest.ScriptInputContent input,
                                  Integer userId, Integer tenantId) {
        var entity = new CmsProductRecord();
        BeanUtils.copyProperties(input, entity);
        entity.setTenantId(tenantId);
        entity.setUserId(userId);
        entity.setTitle(input.getBrand() + input.getProduct());
        entity.setProductName(input.getProduct());
        entity.setSellingPoint(input.getSellingPoint());
        this.saveByEntity(entity, userId, tenantId);
    }

    private void saveByDto(DifyProductSummaryResponseDTO dto, Integer userId, Integer tenantId) {
        var entity = new CmsProductRecord();
        entity.setTenantId(tenantId);
        entity.setUserId(userId);
        entity.setTitle(dto.getBrand() + dto.getProductName());
        entity.setProductName(dto.getProductName());
        entity.setSellingPoint(dto.getSellingPoint());
        this.saveByEntity(entity, userId, tenantId);
    }

    private void saveByEntity(CmsProductRecord entity, Integer userId, Integer tenantId) {
        // 判断标题如果标题已存在，则不保存
        var isExist = baseMapper.exists(new LambdaQueryWrapper<CmsProductRecord>()
                .eq(CmsProductRecord::getTitle, entity.getTitle())
                .eq(CmsProductRecord::getUserId, userId)
                .eq(CmsProductRecord::getTenantId, tenantId)
                .eq(CmsProductRecord::getIsDeleted, 0));
        if (!isExist) {
            save(entity);
        }
    }

    @Override
    public Page<UploadProductVO> videoTaskProductList(Integer current, Integer pageSize, Integer userId, Integer tenantId) {
        var page = this.page(new Page<>(current, pageSize),
                new LambdaQueryWrapper<CmsProductRecord>()
                        .eq(CmsProductRecord::getUserId, userId)
                        .eq(CmsProductRecord::getTenantId, tenantId)
                        .orderByDesc(CmsProductRecord::getCreateTime)
        );
        return PageUtils.convertVOPage(page, this::mapperByCmsProductRecord);
    }

    @Override
    public void deleteProductRecord(Integer recordId, Integer userId, Integer tenantId) {
        var isExist = baseMapper.exists(new LambdaQueryWrapper<CmsProductRecord>()
                .eq(CmsProductRecord::getId, recordId)
                .eq(CmsProductRecord::getUserId, userId)
                .eq(CmsProductRecord::getTenantId, tenantId)
                .eq(CmsProductRecord::getIsDeleted, 0));
        if (isExist) {
            this.removeById(recordId);
        } else {
            throw new BusinessException("删除失败, 不存在该记录");
        }

    }

    private Boolean checkLinkFormat(List<String> urls, String link) {
        // 遍历urls，判断link是否包含url
        for (String url : urls) {
            if (link.contains(url)) {
                return true;
            }
        }
        return false;
    }

    private UploadProductVO mapperByCmsProductRecord(CmsProductRecord entity) {
        var result = new UploadProductVO();
        BeanUtils.copyProperties(entity, result);
        return result;
    }

    /**
     * 提取文件扩展名
     * @param filename 文件路径/文件名（支持带路径的字符串）
     * @return 扩展名（不带点），若无扩展名返回空字符串
     */
    public  String getExtension(String filename) {
        Path path = Paths.get(filename).normalize();
        String fileName = path.getFileName().toString();

        // 跳过隐藏文件（以.开头）
        if (fileName.startsWith(".")) {
            return "";
        }

        int lastDot = fileName.lastIndexOf('.');
        return (lastDot == -1 || lastDot == 0) ? "" : "."+fileName.substring(lastDot + 1);
    }

}




