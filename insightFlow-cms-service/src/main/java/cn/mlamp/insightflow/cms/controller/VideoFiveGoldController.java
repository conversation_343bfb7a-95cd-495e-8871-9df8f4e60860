package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldDetailRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldRetryScriptRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailPageVO;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailVO;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldVO;
import cn.mlamp.insightflow.cms.service.IVideoFiveGoldService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Slf4j
@RequestMapping("/video/five/gold")
@RestController
@RequiredArgsConstructor
@Tag(name = "黄金5秒相关接口")
public class VideoFiveGoldController {

    private final IVideoFiveGoldService videoFiveGoldService;

    @GetMapping("/list")
    @Operation(summary = "获取黄金5秒列表接口")
    public RespBody<Page<VideoFiveGoldVO>> getList(VideoFiveGoldRequest videoFiveGoldRequest) {

        return RespBody.ok(videoFiveGoldService.getList(videoFiveGoldRequest));
    }


    @GetMapping("/list/detail")
    @Operation(summary = "获取黄金5秒详情列表接口")
    public RespBody<VideoFiveGoldDetailPageVO> getListDetail(VideoFiveGoldDetailRequest videoFiveGoldDetailRequest) {
        if(videoFiveGoldDetailRequest.getFiveGoledId() == null){
            return RespBody.fail("fiveGoledId不能为空");
        }
        return RespBody.ok(videoFiveGoldService.getListDetail(videoFiveGoldDetailRequest));
    }


}
