package cn.mlamp.insightflow.cms.queue;

import cn.mlamp.insightflow.cms.constant.RedisConstant;
import cn.mlamp.insightflow.cms.entity.CmsAsyncTask;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.AsyncResultRequest;
import cn.mlamp.insightflow.cms.service.CmsAsyncTaskService;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.strategy.video.create.AnalysisVideoStrategyMap;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Component
@Slf4j
public class RedisQueueConsumer {

    @Resource
    private RedisTemplate<String, AsyncResultRequest> redisTemplate;

    @Resource(name = "analysisThreadExecutor")
    private ExecutorService analysisThreadExecutor;

    @Resource(name = "userAnalysisThreadExecutor")
    private ExecutorService userAnalysisThreadExecutor;

    @Autowired
    private CmsAsyncTaskService cmsAsyncTaskService;

    @Autowired
    private IVideoInfoService videoInfoService;

    @Value("${cms.queue.common:false}")
    private boolean enableQueue;

    @Scheduled(fixedDelay = 1000) // 每5秒执行一次
    public void processAsyncResultQueue() {
        if (!enableQueue) {
            return;
        }
//        log.info("开始处理通用异步结果队列");

        Long size = null;
        String queueKey = RedisConstant.VIDEO_ASYNC_COMMON_RESULT_QUEUE;
        try {
            // 获取队列中的所有任务
            size = redisTemplate.opsForList().size(queueKey);
            if (size == null || size <= 0) {
                return;
            }

            // 批量获取任务
            List<AsyncResultRequest> tasks = redisTemplate.opsForList().range(queueKey, 0, size - 1);

            processTasksAsynchronously(tasks);

            // 只删除已处理的前 size 条消息，保留新进的消息
            redisTemplate.opsForList().trim(queueKey, size, -1);
        } catch (Exception e) {
            log.error("队列处理失败", e);
            redisTemplate.opsForList().trim(queueKey, size, -1);
        }
    }

    /**
     * 异步处理任务方法，使用 analysisThreadExecutor 执行任务
     */
    private void processTasksAsynchronously(List<AsyncResultRequest> tasks) {
        List<Future<?>> futures = new ArrayList<>();
        List<Future<?>> futures2 = new ArrayList<>();

        for (AsyncResultRequest task : tasks) {
            if ("daily".equals(task.getTaskQueue())) {
                // 提交任务到线程池异步执行
                Future<?> future = analysisThreadExecutor.submit(() -> {
                    this.handle(task);
                });
                futures.add(future);
            }

            if ("customer".equals(task.getTaskQueue())) {
                // 提交任务到线程池异步执行
                Future<?> future = userAnalysisThreadExecutor.submit(() -> {
                    this.handle(task);
                });
                futures2.add(future);
            }
        }
    }


    private void handle(AsyncResultRequest task) {
        try {
            log.info("开始异步处理任务: {}", JSONObject.toJSONString(task));
            if(task.getCode()!=200){
                throw new BusinessException("算法接口报错："+task.getMessage());
            }
            // 在这里调用具体的业务处理方法
            CmsAsyncTask cmsAsyncTask = cmsAsyncTaskService.getBaseMapper().selectOne(new LambdaQueryWrapper<CmsAsyncTask>().eq(CmsAsyncTask::getDbUniqueId, task.getDbUniqueId()));
            if (cmsAsyncTask == null) {
                throw new BusinessException(task.getDbUniqueId() + "任务不存在");
            }
            CmsVideoInfo cmsVideoInfo = videoInfoService.getBaseMapper().selectOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId()).eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (cmsVideoInfo == null) {
                throw new BusinessException(cmsAsyncTask.getEsId() + "视频信息不存在");
            }
            //预处理data问题
            Map<String, Object> data = (Map<String, Object>) task.getData();
            task.setData(data.get("data"));

            if("daily".equals(task.getTaskQueue())){
                this.dailyHandle(cmsAsyncTask, task);
            }else if ("customer".equals(task.getTaskQueue())){
                this.customerHandle(cmsAsyncTask, task);
            }
            log.info("任务处理完成: {}", task);
        } catch (Exception e) {
            log.error("异步任务处理失败: {}", task, e);
            //标记失败的任务数据
        }
    }


    private void dailyHandle(CmsAsyncTask cmsAsyncTask, AsyncResultRequest task) {
        AnalysisVideoStrategyMap.processAsyncTask(cmsAsyncTask, task, AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoType());
    }


    private void customerHandle(CmsAsyncTask cmsAsyncTask, AsyncResultRequest task) {
        AnalysisVideoStrategyMap.processAsyncTask(cmsAsyncTask, task, AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType());
    }


}
