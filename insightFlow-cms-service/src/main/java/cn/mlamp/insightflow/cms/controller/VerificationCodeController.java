package cn.mlamp.insightflow.cms.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.util.ObjectUtil;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.helper.RequestDeviceHelper;
import cn.mlamp.insightflow.cms.model.query.CaptchaParam;
import cn.mlamp.insightflow.cms.model.query.VerificationCodeParam;
import cn.mlamp.insightflow.cms.service.CaptchaService;
import cn.mlamp.insightflow.cms.service.VerificationCodeService;
import cn.mlamp.insightflow.cms.util.IpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 验证码Controller
 *
 * <AUTHOR>
 * @since 2022-09-08 15:20:16
 **/
@RestController
@RequestMapping("/verification_code")
@Tag(name = "验证码接口")
public class VerificationCodeController {

    @Resource
    private VerificationCodeService verificationCodeService;
    @Resource
    private CaptchaService captchaService;

    /**
     * 发送验证码
     */
    @PostMapping("/send")
    @Operation(summary = "滑块发送验证码")
    public RespBody<?> send(@Valid @RequestBody VerificationCodeParam verificationCodeParam,
            HttpServletRequest request) {
        if (!RequestDeviceHelper.isMinProgramRequest(request)) {
            CaptchaParam captchaParam = verificationCodeParam.getCaptchaParam();
            if (ObjectUtil.isNull(captchaParam)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR);
            }
            captchaService.checkCaptcha(captchaParam.getRandStr(), captchaParam.getTicket(),
                    IpUtil.getClientIp(request));
        }
        verificationCodeService.send(verificationCodeParam);
        return RespBody.ok();
    }

    /**
     * 移动端发送验证码（无需滑块验证）
     */
    @PostMapping("/mobile/send")
    @Operation(summary = "直接发送验证码")
    public RespBody<?> sendMobile(@Valid @RequestBody VerificationCodeParam verificationCodeParam) {
        verificationCodeService.send(verificationCodeParam);
        return RespBody.ok();
    }
}
