package cn.mlamp.insightflow.cms.model.vo;

import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频任务脚本详情
 */
@Data
public class VideoTaskScriptDetailVO {

    @Schema(description = "任务id", required = true)
    private Integer taskId;

    @Schema(description = "原始视频标题", required = true)
    private String title;

    @Schema(description = "头图url", required = true)
    private String coverImage;

    @Schema(description = "视频url", required = true)
    private String videoUrl;

    @Schema(description = "esId(拼接链接跳转)", required = true)
    private String sourceId;

    @Schema(description = "仿写内容(输入)", required = true)
    private VideoScriptGenRequest.ScriptInputContent contentInput;

/*
    @Schema(description = "参考分镜(输入)", required = true)
    private List<ReferenceStoryBoardVO> referenceStoryboard = new ArrayList<>();
*/

    @Schema(description = "分镜信息", required = true)
    private List<VideoStoryBoardVO> storyBoards = new ArrayList<>();

  /*  @Data
    public static class ReferenceStoryBoardVO {
        @Schema(description = "分镜id", required = true)
        private String id;
        @Schema(description = "分镜名称", required = true)
        private String name;
        @Schema(description = "是否黄金片段", required = true)
        private boolean isGolden;
    }*/
}
