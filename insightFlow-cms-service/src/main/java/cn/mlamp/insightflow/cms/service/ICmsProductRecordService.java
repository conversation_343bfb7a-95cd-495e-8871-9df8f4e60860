package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsProductRecord;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.UploadFileVo;
import cn.mlamp.insightflow.cms.model.vo.UploadProductVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;


/**
* @description 针对表【cms_product_record(商品记录表)】的数据库操作Service
*/
public interface ICmsProductRecordService extends IService<CmsProductRecord> {

    UploadProductVO videoTaskProductUrl(TaskUploadVO uploadVO, Integer userId, Integer tenantId);

    void saveByScriptInput(VideoScriptGenRequest.ScriptInputContent input, Integer userId, Integer tenantId);

    Page<UploadProductVO> videoTaskProductList(Integer current, Integer pageSize, Integer userId, Integer tenantId);

    void deleteProductRecord(Integer recordId, Integer userId, Integer tenantId);

    UploadFileVo videoTaskUploadProductFile(MultipartFile file,Integer userId, Integer tenantId);



}
