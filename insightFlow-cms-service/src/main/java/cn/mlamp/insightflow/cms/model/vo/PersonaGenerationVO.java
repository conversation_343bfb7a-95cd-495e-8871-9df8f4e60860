package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 人物画像生成响应
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@Schema(description = "人物画像生成响应")
public class PersonaGenerationVO {

    @Schema(description = "ES拉取的社交媒体数据", required = true)
    private String socialMediaData;

    @Schema(description = "解析后的用户故事列表", required = true)
    private List<UserStoryVO> personaResult;

    @Schema(description = "品牌", required = true)
    private String brand;

    @Schema(description = "产品", required = true)
    private String product;

    @Schema(description = "卖点", required = true)
    private String sellingPoint;
}
