package cn.mlamp.insightflow.cms.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.entity.TenantToken;
import cn.mlamp.insightflow.cms.entity.TokenRechargeDetail;
import cn.mlamp.insightflow.cms.entity.TokenUseDetail;
import cn.mlamp.insightflow.cms.enums.TokenTaskTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.TenantTokenMapper;
import cn.mlamp.insightflow.cms.mapper.TokenUseDetailMapper;
import cn.mlamp.insightflow.cms.model.query.PageRequest;
import cn.mlamp.insightflow.cms.model.query.TokenDetailsPageQueryRequest;
import cn.mlamp.insightflow.cms.model.vo.ConsumptionSummaryVO;
import cn.mlamp.insightflow.cms.model.vo.RechargeAllVO;
import cn.mlamp.insightflow.cms.model.vo.TenantTokenSummaryVO;
import cn.mlamp.insightflow.cms.model.vo.TokenDetailsVO;
import cn.mlamp.insightflow.cms.service.TenantTokenService;
import cn.mlamp.insightflow.cms.service.TokenRechargeDetailService;
import cn.mlamp.insightflow.cms.service.TokenUseDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantTokenServiceImpl extends ServiceImpl<TenantTokenMapper, TenantToken> implements TenantTokenService {

    private final TenantTokenMapper tenantTokenMapper;

    @Lazy
    @Autowired
    private TokenUseDetailService tokenUseDetailService;

    @Autowired
    private TokenUseDetailMapper tokenUseDetailMapper;

    @Autowired
    private TokenRechargeDetailService tokenRechargeDetailService;

    @Override
    public TenantTokenSummaryVO getSummary(Integer tenantId) {
        // 查询当前租户的 token 余额
        // 使用 LambdaQueryWrapper 查询 balance
        LambdaQueryWrapper<TenantToken> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TenantToken::getBalance).eq(TenantToken::getTenantId, tenantId);

        // 查询数据
        TenantToken token = tenantTokenMapper.selectOne(queryWrapper);
        Integer balance = token != null ? token.getBalance() : 1000000;

        Integer totalToken = tokenUseDetailMapper.selectTotalTokens(tenantId);
        // 获取本月第一天
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date firstDayOfMonth = calendar.getTime();

        Integer monthlyToken = tokenUseDetailMapper.selectMonthlyTokens(tenantId, firstDayOfMonth);
        TenantTokenSummaryVO summaryVO = new TenantTokenSummaryVO();
        summaryVO.setAccumulatedExpenses(totalToken);
        summaryVO.setMonthExpenses(monthlyToken);
        summaryVO.setTenantId(tenantId);
        summaryVO.setBalance(balance - totalToken);
        summaryVO.setAccumulatedRecharge(balance);
        return summaryVO;
    }

    @Override
    public ConsumptionSummaryVO consumptionSummary(Integer tenantId) {
        LambdaQueryWrapper<TokenUseDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TokenUseDetail::getTenantId, tenantId);
        List<TokenUseDetail> tokenUseDetails = tokenUseDetailMapper.selectList(queryWrapper);
        Integer totalToken = 0;
        for (TokenUseDetail tokenUseDetail : tokenUseDetails) {
            Integer tokens = tokenUseDetail.getTokens();
            totalToken += tokens;
        }
        ConsumptionSummaryVO consumptionSummaryVO = new ConsumptionSummaryVO();
        consumptionSummaryVO.setAccumulatedExpenses(totalToken);
        return consumptionSummaryVO;
    }

    @Override
    public Page<TokenDetailsVO> detailPageQuery(TokenDetailsPageQueryRequest request) {
        Integer tenantId = request.getTenantId();
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        Integer pageSize = request.getPageSize();
        Integer current = request.getCurrent();
        List<String> sortFieldList = request.getSortFieldList();
        List<String> sortOrderList = request.getSortOrderList();

        QueryWrapper<TokenUseDetail> queryWrapper = new QueryWrapper<>();
        if (tenantId != null)
            queryWrapper.eq("tenant_id", tenantId);
        if (startDate != null && endDate != null) {
            queryWrapper.between("usage_time", startDate, endDate);
        } else if (startDate != null) {
            queryWrapper.ge("usage_time", startDate); // ge表示大于等于
        } else if (endDate != null) {
            queryWrapper.le("usage_time", endDate); // le表示小于等于
        }

        List<TokenUseDetail> tokenUseDetailList = tokenUseDetailService.list(queryWrapper);

        // 对tokenUseDetailList做一个合并操作
        // 合并 observation_id 相同的记录
        Map<String, TokenDetailsVO> mergedMap = new HashMap<>();
        for (TokenUseDetail detail : tokenUseDetailList) {
            String observationId = detail.getTraceId();
            if (mergedMap.containsKey(observationId)) {
                // 如果已经存在，合并 tokens
                TokenDetailsVO existingVO = mergedMap.get(observationId);
                existingVO.setTokens(existingVO.getTokens() + detail.getTokens());
            } else {
                // 如果不存在，创建新的 TokenDetailsVO 并添加
                TokenDetailsVO tokenDetailsVO = new TokenDetailsVO();
                tokenDetailsVO.setTokens(detail.getTokens());
                tokenDetailsVO.setTaskName(detail.getTaskName());
                tokenDetailsVO.setTaskType(TokenTaskTypeEnum.getShowTaskType(detail.getTaskType()));
                tokenDetailsVO.setUserName(detail.getUserName());
                tokenDetailsVO.setUsageTime(detail.getUsageTime());
                // tokenDetailsVO.setTenantId(detail.getTenantId()); // 确保 TenantId 也被设置
                mergedMap.put(observationId, tokenDetailsVO);
            }
        }

        // 将合并后的结果转换为列表
        List<TokenDetailsVO> mergedList = new ArrayList<>(mergedMap.values());
        // 如果有排序需求，在合并后的列表上重新排序
        if (sortFieldList != null) {
            Comparator<TokenDetailsVO> comparator = null;
            for (int i = 0; i < sortFieldList.size(); i++) {
                String sortField = sortFieldList.get(i);
                String sortOrder = sortOrderList.get(i);
                Comparator<TokenDetailsVO> fieldComparator = getComparatorByField(sortField, sortOrder);
                comparator = (comparator == null) ? fieldComparator : comparator.thenComparing(fieldComparator);
            }
            if (comparator != null) {
                mergedList.sort(comparator);
            }
        }
        // 重新分页处理
        int totalSize = mergedList.size();
        int start = (current - 1) * pageSize;
        int end = Math.min(start + pageSize, totalSize);
        List<TokenDetailsVO> pageList = mergedList.subList(start, end);

        // 创建分页结果
        Page<TokenDetailsVO> tokenDetailsVOPage = new Page<>(current, pageSize);
        tokenDetailsVOPage.setRecords(pageList);
        tokenDetailsVOPage.setTotal((long) totalSize);

        return tokenDetailsVOPage;
    }

    private Comparator<TokenDetailsVO> getComparatorByField(String sortField, String sortOrder) {
        Comparator<TokenDetailsVO> comparator;
        switch (sortField) {
        case "tokens":
            comparator = Comparator.comparing(TokenDetailsVO::getTokens);
            break;
        case "task_name":
            comparator = Comparator.comparing(TokenDetailsVO::getTaskName);
            break;
        case "task_type":
            comparator = Comparator.comparing(TokenDetailsVO::getTaskType);
            break;
        case "user_name":
            comparator = Comparator.comparing(TokenDetailsVO::getUserName);
            break;
        case "usage_time":
            comparator = Comparator.comparing(TokenDetailsVO::getUsageTime);
            break;
        default:
            throw new IllegalArgumentException("Invalid sort field: " + sortField);
        }
        return sortOrder.equals(PageRequest.SORT_ORDER_ASC) ? comparator : comparator.reversed();
    }

    @Override
    public Page<RechargeAllVO> allPageQuery(TokenDetailsPageQueryRequest request) {
        return null;
    }

    @Override
    public Integer checkBalance(Integer tenantId) {
        // 查询当前租户的 token 余额
        LambdaQueryWrapper<TenantToken> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TenantToken::getBalance).eq(TenantToken::getTenantId, tenantId);

        // 查询数据
        TenantToken token = tenantTokenMapper.selectOne(queryWrapper);
        Integer balance = token != null ? token.getBalance() : 1000000;

        // 查询已消耗的token总数
        Integer totalToken = tokenUseDetailMapper.selectTotalTokens(tenantId);

        // 计算实际余额
        return balance - totalToken;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rechargeToken(Integer tenantId, String tenantName, Integer rechargeTokens, Integer userId) {
        // 1. 查询租户当前余额
        TenantToken tenantToken = this
                .getOne(new LambdaQueryWrapper<TenantToken>().eq(TenantToken::getTenantId, tenantId));

        // 租户必须存在，否则报错
        if (tenantToken == null) {
            log.error("租户不存在，无法进行充值，tenantId: {}", tenantId);
            throw new BusinessException(RespCode.FAIL.getCode(), "租户不存在，无法进行充值");
        }

        // 2. 更新租户余额
        Integer currentBalance = tenantToken.getBalance();
        tenantToken.setBalance(currentBalance + rechargeTokens);
        tenantToken.setAccumulatedRecharge(tenantToken.getAccumulatedRecharge() + rechargeTokens);
        tenantToken.setStatisticalTime(new Date());
        this.updateById(tenantToken);

        // 3. 创建充值记录，不维护余额字段
        TokenRechargeDetail rechargeDetail = new TokenRechargeDetail();
        rechargeDetail.setTenantId(tenantId);
        rechargeDetail.setTenantName(tenantName);
        rechargeDetail.setUserId(userId);
        rechargeDetail.setRechargeTokens(rechargeTokens);
        rechargeDetail.setRechargeTime(new Date());
        tokenRechargeDetailService.save(rechargeDetail);

        log.info("租户Token充值成功，租户ID: {}, 充值数量: {}, 当前余额: {}", tenantId, rechargeTokens, tenantToken.getBalance());
    }
}
