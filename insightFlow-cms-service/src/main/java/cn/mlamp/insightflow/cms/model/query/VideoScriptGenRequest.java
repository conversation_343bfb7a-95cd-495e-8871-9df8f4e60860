package cn.mlamp.insightflow.cms.model.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AI仿写和脚本生成请求
 */
@Data
public class VideoScriptGenRequest {

    @Schema(description = "解码任务id", required = true)
    private Integer taskId;

    @Schema(description = "脚本来源id（黄金5s总结id或者视频id）", required = true)
    private String sourceId;

    @Schema(description = "仿写内容输入", required = true)
    private ScriptInputContent content;

    @Schema(description = "AI解码结果", required = true)
    private Map<String, List<String>> decodeResult = new HashMap<>();

    @Schema(description = "已选分镜Id数组(AI仿写必传)")
    private List<String> storyboardIds = new ArrayList<>();

    private Integer userId;
    private Integer tenantId;

    @Data
    public static class ScriptInputContent {
        @Schema(description = "品牌", required = true)
        private String brand;
        @Schema(description = "产品", required = true)
        private String product;
        @Schema(description = "时长(秒)", required = true)
        private Integer duration;
        @Schema(description = "镜头数", required = true)
        private Integer lensNum;
        @Schema(description = "卖点")
        private String sellingPoint;
        @Schema(description = "场景")
        private String scene;
        @Schema(description = "人数")
        private Integer peopleNum;
        @Schema(description = "节日")
        private String festival;
        @Schema(description = "受众人群")
        private String targetAudience;
    }
}
