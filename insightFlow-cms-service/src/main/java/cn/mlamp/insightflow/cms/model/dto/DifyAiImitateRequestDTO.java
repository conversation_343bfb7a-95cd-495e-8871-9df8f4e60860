package cn.mlamp.insightflow.cms.model.dto;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import lombok.Data;

@Data
public class DifyAiImitateRequestDTO {
    private String brand;
    private String product;
    private String sellingPoint;
    private String scene;
    private String targetAudience;              //受众人群

    private String actions; //分镜json（必填）

    // 后期备选字段
    private String brandIntegration;      // 品牌植入
    private String cameraType;            // 镜头类型
    private String cameraView;            // 运镜方式
    private String actors;                // 出现演员
    private String actorActions;          // 演员动作
    private String actorExpressions;      // 演员表情
    private String dialogue;              // 台词
    private String dialogueEmotion;       // 台词情绪
    private String costumeSuggestions;    // 服装造型
    private String props;                 // 道具清单
    private String setRequirements;       // 布景要求
    private String bgm;                   // 背景音乐/音效
    private String equipment;             // 摄影器材

    // 视频策略改
    private String startGold3s; //开篇黄金三秒
    private String duringSellingScene; //中段卖点场景
    private String endSellingDialogue; //结尾逼单话术

    public static Map<String, Object> buildDifyParams(VideoScriptGenRequest videoScriptGenRequest, String storyBoardJson) {
        var dto = new DifyAiImitateRequestDTO();
        var content = videoScriptGenRequest.getContent();
        // 必填
        dto.setBrand(content.getBrand());
        dto.setProduct(content.getProduct());
        // 选填
        dto.setSellingPoint(content.getSellingPoint());
        dto.setScene(content.getScene());
        dto.setTargetAudience(content.getTargetAudience());

        // actions分镜json
        if (StrUtil.isEmpty(storyBoardJson)) {
            throw new RuntimeException("参考分镜不能为空");
        }
        dto.setActions(storyBoardJson);

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(dto, Map.class);
    }
}
