package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户故事VO
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@Schema(description = "用户故事")
public class UserStoryVO {

    @Schema(description = "具体人设", required = true)
    private String persona;

    @Schema(description = "对应生活化场景", required = true)
    private String lifeScene;

    @Schema(description = "对应痛点", required = true)
    private String painPoint;

    @Schema(description = "对应产品卖点", required = true)
    private String productSellingPoint;

    @Schema(description = "对应通用金句", required = true)
    private String slogan;

    @Schema(description = "对应产品昵称", required = true)
    private String productNickname;
}
