package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 移动端用户信息VO
 */
@Data
public class MobileInfoVO {

    @Schema(description = "租户名称", required = true)
    private String tenantName;

    @Schema(description = "租户截止日期", required = true)
    private String tenantDDL;

    @Schema(description = "手机号", required = true)
    private String userPhone;

    @Schema(description = "邮箱", required = true)
    private String userEmail;

    @Schema(description = "头像", required = true)
    private String userPhoto;

    @Schema(description = "余额", required = true)
    private String tokens;
}
