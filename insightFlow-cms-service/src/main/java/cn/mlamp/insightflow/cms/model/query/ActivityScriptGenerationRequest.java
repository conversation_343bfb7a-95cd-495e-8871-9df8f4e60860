package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 活动逼单话术生成请求
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Schema(description = "活动逼单话术生成请求")
public class ActivityScriptGenerationRequest {

    @Schema(description = "活动列表", required = true, example = "[\"双11大促\", \"年终清仓\", \"新品发布\"]")
    @NotEmpty(message = "活动列表不能为空")
    private List<String> activities;
}
