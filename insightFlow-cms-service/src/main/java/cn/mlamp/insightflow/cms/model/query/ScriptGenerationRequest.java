package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 脚本生成请求
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Schema(description = "脚本生成请求")
public class ScriptGenerationRequest {

    @Schema(description = "品牌", required = true, example = "小米")
    @NotBlank(message = "品牌不能为空")
    private String brand;

    @Schema(description = "产品", required = true, example = "小米手机")
    @NotBlank(message = "产品不能为空")
    private String product;

    @Schema(description = "卖点", required = true, example = "高性价比")
    @NotBlank(message = "卖点不能为空")
    private String sellingPoint;

    @Schema(description = "用户故事", required = true, example = "年轻人追求性价比的故事")
    @NotBlank(message = "用户故事不能为空")
    private String userStory;

    @Schema(description = "逼单话术", required = true, example = "限时优惠，错过再等一年")
    @NotBlank(message = "逼单话术不能为空")
    private String purchaseWording;

    @Schema(description = "画面套路", required = true, example = "产品展示+使用场景")
    @NotBlank(message = "画面套路不能为空")
    private String picture;

    @Schema(description = "台词套路", required = true, example = "开场引入+产品介绍+逼单")
    @NotBlank(message = "台词套路不能为空")
    private String wording;

    private Integer userId;
    private Integer tenantId;
}
