package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 任务详情表;
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_task_detail")
public class CmsTaskDetail extends BaseEntity{
    /** id */
    @TableId(type = IdType.AUTO)
    private Integer id ;
    /** 任务id */
    private Integer taskId ;
    /** 数据类型 0输入 1输出 */
    private Integer type ;
    /** 数据类型 0 dify 1 算法 4 vidu视频 */
    private String dataType ;
    /** json */
    private String data ;


}
