
package cn.mlamp.insightflow.cms.model.vo;

import java.util.Date;

import lombok.Data;

@Data
public class ConsumptionVO {
    private String traceId;
    private Integer taskId;
    private String taskName;
    private Integer tenantId;
    private String userName; // 替换原来的userId
    private Integer tokens;
    private Date usageTime;
    private String taskType; // 替换原来的taskType数字
    private String headPic;
}
