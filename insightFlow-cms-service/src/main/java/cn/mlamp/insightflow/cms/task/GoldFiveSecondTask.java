package cn.mlamp.insightflow.cms.task;

import cn.mlamp.insightflow.cms.common.redis.LockService;
import cn.mlamp.insightflow.cms.config.TaskConfig;
import cn.mlamp.insightflow.cms.entity.CmsPullTask;
import cn.mlamp.insightflow.cms.service.CmsPullTaskService;
import cn.mlamp.insightflow.cms.service.IGoldFiveSecondTaskService;
import cn.mlamp.insightflow.cms.util.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 黄金五秒定时任务
 *
 * <AUTHOR> yang<PERSON><PERSON>
 * @date : 2025-5-15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class GoldFiveSecondTask {

    private final IGoldFiveSecondTaskService goldFiveSecondTaskService;

    @Autowired
    private LockService lockService;

    @Autowired
    private CmsPullTaskService cmsPullTaskService;

    @Autowired
    private  TaskConfig taskConfig;

    /**
     * 每天早上8点10分执行黄金五秒任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void goldFiveSecondJob() {
        if (!taskConfig.isCommonEnable()) {
            return;
        }
        log.info("开始执行黄金五秒定时任务");

        // 生成锁 key（按日期区分）
        String dateStr = DateUtil.getYYYYMMDD(new Date());
        String lockName = "gold_five_second_job_" + dateStr;
        RLock lock = lockService.getLock(lockName);

        try {
            // 尝试获取锁，最多等待0秒，锁的持有时间为24小时
            boolean isLocked = lock.tryLock(0, 24, TimeUnit.HOURS);
            if (!isLocked) {
                log.warn("已有其他实例在执行 {} 的黄金五秒任务，跳过执行", dateStr);
                return;
            }

            log.info("开始执行黄金五秒定时任务");

            // 创建任务记录
            CmsPullTask task = new CmsPullTask();
            task.setStartTime(new Date());
            task.setStatus("running");
            task.setType("GoldFiveSecond");
            task.setPullParams("{}");
            cmsPullTaskService.save(task);
            log.info("创建黄金五秒任务记录，taskId: {}", task.getId());

            try {
                // 执行黄金五秒任务
                goldFiveSecondTaskService.processGoldFiveSecondTask(dateStr);

                // 更新任务状态为完成
                LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(CmsPullTask::getStatus, "completed").set(CmsPullTask::getEndTime, new Date())
                        .eq(CmsPullTask::getId, task.getId());
                cmsPullTaskService.update(updateWrapper);

                log.info("黄金五秒定时任务执行完成");
            } catch (Exception e) {
                // 更新任务状态为失败
                LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(CmsPullTask::getStatus, "failed").set(CmsPullTask::getEndTime, new Date())
                        .set(CmsPullTask::getFailMessage, e.getMessage()).eq(CmsPullTask::getId, task.getId());
                cmsPullTaskService.update(updateWrapper);

                // 重新抛出异常，让外层 catch 捕获
                throw e;
            }
        } catch (InterruptedException e) {
            log.error("获取锁时被中断", e);
        } finally {
            // 如果当前线程持有锁，则释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
