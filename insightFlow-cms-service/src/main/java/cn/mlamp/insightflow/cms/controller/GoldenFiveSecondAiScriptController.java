package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.query.ActivityScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.PersonaGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.vo.ActivityScriptGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.PersonaGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.ScriptGenerationVO;
import cn.mlamp.insightflow.cms.service.ICmsTaskInfoService;
import cn.mlamp.insightflow.cms.service.IGoldenFiveSecondAiScriptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 黄金5秒AI脚本生成控制器
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Slf4j
@RestController
@RequestMapping("/golden-five-second/ai-script")
@RequiredArgsConstructor
@Tag(name = "黄金5秒AI脚本生成接口")
public class GoldenFiveSecondAiScriptController {

    private final IGoldenFiveSecondAiScriptService goldenFiveSecondAiScriptService;
    private final ICmsTaskInfoService taskInfoService;

    @PostMapping("/generate-persona")
    @Operation(summary = "生成人物画像")
    public RespBody<PersonaGenerationVO> generatePersona(@RequestBody @Valid PersonaGenerationRequest request) {

        PersonaGenerationVO result = goldenFiveSecondAiScriptService.generatePersona(request);

        return RespBody.ok(result);
    }

    @PostMapping("/generate-activity-script")
    @Operation(summary = "生成活动逼单话术")
    public RespBody<ActivityScriptGenerationVO> generateActivityScript(
            @RequestBody @Valid ActivityScriptGenerationRequest request) {

        ActivityScriptGenerationVO result = goldenFiveSecondAiScriptService.generateActivityScript(request);

        return RespBody.ok(result);
    }

    @PostMapping("/generate-script")
    @Operation(summary = "生成脚本")
    public RespBody<ScriptGenerationVO> generateScript(@RequestBody @Valid ScriptGenerationRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);

        ScriptGenerationVO result = goldenFiveSecondAiScriptService.generateScript(request);

        // 异步执行脚本生成
        taskInfoService.scriptGenerationAsync(request, Integer.valueOf(result.getTaskId()));

        return RespBody.ok(result);
    }
}
