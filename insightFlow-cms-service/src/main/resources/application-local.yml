logging:
  level:
    root: info
    com.baomidou.mybatisplus: DEBUG
    org.mybatis: DEBUG

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto

spring:
  servlet:
    multipart:
      # 是否开启springMVC 多部分上传功能(默认开启)
      enabled: true
      # 上传单个文件大小(默认是1MB)
      max-file-size: 1024MB
      # 限制所有文件大小
      max-request-size: 1024MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************
    username: ai_image_test
    password: OEATCzRAM1
    hikari:
      maximum-pool-size: 10
      connection-init-sql: "SELECT 1"
  data:
    redis:
      database: 3
      timeout: 30000
      #password: gpqylk3p6l
      host: localhost
      port: 6379
  ai:
    openai:
      base-url: https://llm-gateway.mininglamp.com/
      api-key: sk-LtXa0qWUmHCV50mo68Be52D27f6a475c979f006c3b615379
      embedding:
        options:
          model: text-embedding-ada-002

# 对象存储服务配置
s3:
  config:
    # 图片
#    pic:
#      aliasBucketName: ai-pc-cms2
#      bucketName: ai-pc-cms2
#      endpoint: https://oss-cn-beijing.aliyuncs.com
#      accessKey: LTAI5tMoPYdd2FtfVtKR9qFT
#      region: oss-cn-beijing
#      pathStyle: false
#      secretKey: ******************************

    cms:
      aliasBucketName: ai-pc-cms2
      bucketName: ai-pc-cms2
      endpoint: https://oss-cn-beijing.aliyuncs.com
      accessKey: LTAI5tMoPYdd2FtfVtKR9qFT
      region: oss-cn-beijing
      pathStyle: false
      secretKey: ******************************

  # 文档对象存储服务配置
  document:
    external:
      service: true
    download:
      expireSecond: 900
    upload:
      expireSecond: 900

login:
  session:
    active: false

#image-server:
#  upload-address: http://************:8008/upload_image
#  combine-address: http://************:8008/combine
#  flux1-address: http://************:1607/generate/flux1
#  sd3-address: http://************:1607/generate/sd3
#  role-generation-address: http://************:8321/generate_image
#  role-upload-address: http://************:8321/upload_image
#
analysis-server:
#  cluster-address: http://*************:8234/cluster_sync
#  ascribe-address: http://*************:8112/attribution_analysis
#  model-cluster-address: https://llm-ops-social.mlamp.cn/v1
#  model-cluster-api: app-X1UtJyi42uHNzZyp4zzxY8Jw
#  marking-address: http://************:8166/batch-api
  token-use-address: https://ai-gateway.mininglamp.com/api/token/usage

#ascribe-authentication:
#  token: Bearer Attribution_Analysis-4f3a2b1c9d8e7f6a5b4c3d2e1f0a9b8c

auth-center:
  anonymousPath: /ttc/login
  baseDomain: mlamp.cn
  loginPath: /ttc/login
  logoutPath: /ttc/logout
  productKey: IFCMS
  secure: false
  serverUrl: https://ttc-test.cn.miaozhen.com/ttc/api

#tcc:
#  tenant-id: 8540
#  client-id: "insight-flow"
#  productKey: "insight-flow"
#  url: "https://ttc-test.cn.miaozhen.com/ttc/"
#  clientSecret: "597dc305-5a78-b97b-dead-25e82902dd64"
#  resourceTypeKeyAgent: "agent"
#  resourceTypeKeyPrompt: "prompt"

# 数据库配置（PostgreSQL）
database:
  postgres:
    driver-class-name: org.postgresql.Driver
    url: *********************************************
    username: cms
    password: cms

  mysql:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************
    username: ai_image_test
    password: OEATCzRAM1
    hikari:
      maximum-pool-size: 10
      connection-init-sql: "SELECT 1"

video:
  download:
    baseUrl: https://deepana.hsk.top


analysis:
  video:
    ffmpeg: /opt/homebrew/bin/ffmpeg
    ffprobe: /opt/homebrew/bin/ffprobe


es:
  client:
    es7:
      hosts: https://social-es.mlamp.cn
      path-prefix: "mz_social"
      api-key: "Y2EzU1JKUUI4UUs3cnVwWEtmRlo6dUtPRUhRRGFSU0NuakctYmRkd2d1Zw=="                       # 如果需要，提供你的 API 密钥
      connect-timeout: 5000
      socket-timeout: 300000
      connection-request-timeout: 10000
      max-conn-total: 30
      max-conn-per-route: 10
      keep-alive-strategy-minute: 3
      bulk-timeout: 120000

dify:
  base-url: https://llm-ops-social.mlamp.cn/v1
  storyboard-recommend-key: app-takZLFN5Iss20WD2eGbl71gM
  script-gen-key: app-g1FdvpaoYHFeFrPg856cM68C
  ai-write-key: app-QEEKD2FKeaJwADa4zXjsMB6X
  image-prompt-translate-key: app-t9Emy5DGySRKC2jKZUzpM8M3
  gold-five-second-key: app-wSUOvJ7ubDYCadclyxkXx1KJ
  product-summary-key: app-1OyXih78Vl1JRMGmX5yoJLKB
  storyboard-modify-key: app-Ss6WWNShKgchGAq3f6BBW0MW
  imageStyleAndDescKey: app-M3wI0s78fYCY6uJyopupUdCB
  persona-generation-key: app-DKQaRL2ToO7bx5dw1WSZHtOV
  activity-script-generation-key: app-XMntyWFO69cegdzOLfZye4PT
  golden-five-second-script-gen-key: app-cZyLBQVtTkxe2XCJSg40OjMD
